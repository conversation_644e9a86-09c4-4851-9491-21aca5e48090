import os
import base64
import has<PERSON>ib
import hmac
from Cryptodome.Cipher import <PERSON><PERSON>, <PERSON>Cha<PERSON>
from Cryptodome.Util.Padding import pad, unpad
from Cryptodome.Random import get_random_bytes
from Cryptodome.Protocol.KDF import scrypt

class QuantumSafeEncryption:
    """
    A hybrid encryption system combining multiple cryptographic primitives
    for maximum security and performance.
    """
    
    def __init__(self, password: str, salt: bytes = None):
        """
        Initialize the encryption engine with a password and optional salt.
        
        Args:
            password: The master password for key derivation
            salt: Optional salt for key derivation (random if not provided)
        """
        self.salt = salt if salt else get_random_bytes(32)
        self.key_material = self._derive_keys(password)
        self.key1 = self.key_material[:32]  # AES-256 key
        self.key2 = self.key_material[32:64]  # ChaCha20 key
        self.hmac_key = self.key_material[64:96]  # HMAC key
        
    def _derive_keys(self, password: str) -> bytes:
        """Derive multiple strong keys using scrypt KDF."""
        # Using scrypt with high security parameters
        return scrypt(
            password.encode('utf-8'),
            salt=self.salt,
            key_len=96,  # 3x32 bytes for different keys
            N=2**20,    # CPU/memory cost parameter
            r=8,        # Block size parameter
            p=1         # Parallelization parameter
        )
    
    def encrypt(self, plaintext: bytes) -> bytes:
        """Encrypt data with multiple layers of encryption."""
        # Layer 1: AES-256 in GCM mode
        aes_nonce = get_random_bytes(12)
        aes_cipher = AES.new(self.key1, AES.MODE_GCM, nonce=aes_nonce)
        ciphertext1, aes_tag = aes_cipher.encrypt_and_digest(plaintext)
        
        # Layer 2: ChaCha20
        chacha_nonce = get_random_bytes(12)
        chacha_cipher = ChaCha20.new(key=self.key2, nonce=chacha_nonce)
        ciphertext2 = chacha_cipher.encrypt(ciphertext1)
        
        # Combine all components
        combined = (
            self.salt +
            aes_nonce + 
            chacha_nonce +
            aes_tag +
            ciphertext2
        )
        
        # Add HMAC for integrity
        hmac_digest = hmac.new(self.hmac_key, combined, 'sha256').digest()
        
        return base64.b64encode(hmac_digest + combined)
    
    def decrypt(self, encrypted_data: bytes) -> bytes:
        """Decrypt data with multiple layers of decryption."""
        try:
            # Decode base64
            decoded = base64.b64decode(encrypted_data)
            
            # Verify HMAC
            received_hmac = decoded[:32]
            combined = decoded[32:]
            expected_hmac = hmac.new(self.hmac_key, combined, 'sha256').digest()
            
            if not hmac.compare_digest(received_hmac, expected_hmac):
                raise ValueError("HMAC verification failed - data may be corrupted")
            
            # Extract components
            salt = combined[:32]
            aes_nonce = combined[32:44]
            chacha_nonce = combined[44:56]
            aes_tag = combined[56:72]
            ciphertext = combined[72:]
            
            # Reinitialize with stored salt
            if salt != self.salt:
                self.salt = salt
                self.key_material = self._derive_keys("")  # Password not needed as we already have the salt
                self.key1 = self.key_material[:32]
                self.key2 = self.key_material[32:64]
                self.hmac_key = self.key_material[64:96]
            
            # Layer 2: ChaCha20 decryption
            chacha_cipher = ChaCha20.new(key=self.key2, nonce=chacha_nonce)
            decrypted1 = chacha_cipher.decrypt(ciphertext)
            
            # Layer 1: AES-GCM decryption
            aes_cipher = AES.new(self.key1, AES.MODE_GCM, nonce=aes_nonce)
            plaintext = aes_cipher.decrypt_and_verify(decrypted1, aes_tag)
            
            return plaintext
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")

class FileEncryptor:
    """Handles file encryption/decryption operations."""
    
    def __init__(self, password: str):
        self.crypto = QuantumSafeEncryption(password)
    
    def encrypt_file(self, input_path: str, output_path: str) -> None:
        """Encrypt a file."""
        with open(input_path, 'rb') as f:
            plaintext = f.read()
        
        encrypted = self.crypto.encrypt(plaintext)
        
        with open(output_path, 'wb') as f:
            f.write(encrypted)
    
    def decrypt_file(self, input_path: str, output_path: str) -> None:
        """Decrypt a file with better error handling and messages."""
        try:
            # Read the encrypted file
            with open(input_path, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                raise ValueError("The file is empty")
            
            # Try to decode as base64 first
            try:
                decoded = base64.b64decode(encrypted_data)
                if len(decoded) < 96:  # Minimum size for HMAC + salt + nonce + tag
                    raise ValueError("Invalid encrypted file format")
                    
                # Extract salt from the file (bytes 32-64 after HMAC)
                salt = decoded[32:64]
                
                # Create a new crypto instance with the extracted salt
                self.crypto = QuantumSafeEncryption("", salt)
                
                # Now try to decrypt with the proper salt
                try:
                    decrypted = self.crypto.decrypt(encrypted_data)
                    
                    # Verify the decrypted data is valid
                    if not decrypted:
                        raise ValueError("Decryption resulted in empty data")
                        
                    # Write the decrypted data
                    with open(output_path, 'wb') as f:
                        f.write(decrypted)
                        
                except ValueError as e:
                    if "HMAC verification failed" in str(e):
                        raise ValueError("Incorrect password or corrupted file. Please check your password and try again.")
                    raise
                    
            except (ValueError, IndexError) as e:
                # If base64 decoding fails, try with the original crypto instance
                try:
                    decrypted = self.crypto.decrypt(encrypted_data)
                    if not decrypted:
                        raise ValueError("Decryption resulted in empty data")
                        
                    with open(output_path, 'wb') as f:
                        f.write(decrypted)
                except Exception as inner_e:
                    if "HMAC" in str(inner_e):
                        raise ValueError("Incorrect password or corrupted file. Please check your password and try again.")
                    raise
                    
        except FileNotFoundError:
            raise FileNotFoundError("Input file not found")
        except PermissionError:
            raise PermissionError("Permission denied when accessing files")
        except Exception as e:
            if "HMAC" in str(e):
                raise ValueError("Incorrect password or corrupted file. Please check your password and try again.")
            raise ValueError(f"Failed to decrypt file: {str(e)}")
