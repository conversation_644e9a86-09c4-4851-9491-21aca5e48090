from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for, flash, make_response
from flask_wtf.csrf import CSRFProtect, generate_csrf
from crypto_engine import QuantumSafeEncryption, FileEncryptor
from database import db, EncryptionHistory, SavedText
import os
import base64
import json
import uuid
from datetime import datetime, timedelta
import time
from werkzeug.utils import secure_filename
from functools import wraps

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['ALLOWED_EXTENSIONS'] = {
    # Encrypted files
    'encrypted',
    # Documents
    'txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp',
    # Images
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff',
    # Archives
    'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz',
    # Media
    'mp3', 'wav', 'ogg', 'mp4', 'webm', 'mov', 'avi', 'mkv',
    # Executables
    'exe', 'msi', 'dmg', 'app', 'apk',
    # Code
    'py', 'js', 'html', 'css', 'php', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'go', 'rs', 'rb', 'sh', 'bat', 'ps1',
    # Data
    'json', 'xml', 'csv', 'sql', 'db', 'sqlite', 'sqlite3', 'mdb', 'accdb',
    # Other common
    'iso', 'dll', 'sys', 'ini', 'cfg', 'conf', 'log',
    # All files (for decryption)
    '*'
}
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'encryption_history.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize extensions
csrf = CSRFProtect(app)
db.init_app(app)

# Create database tables
with app.app_context():
    db.create_all()

def allowed_file(filename):
    # If the action is decryption, allow all file types
    if request.form.get('action') == 'decrypt':
        return True
    # For encryption, check against allowed extensions
    return '.' in filename and \
           (filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS'] or 
            '*' in app.config['ALLOWED_EXTENSIONS'])

def log_operation(operation_type, content_type, original_name, result_name):
    """Log an encryption/decryption operation to the database"""
    try:
        history = EncryptionHistory(
            operation_type=operation_type,
            content_type=content_type,
            original_name=original_name,
            result_name=result_name
        )
        db.session.add(history)
        db.session.commit()
    except Exception as e:
        app.logger.error(f"Error logging operation: {str(e)}")
        db.session.rollback()

def get_history():
    """Retrieve encryption/decryption history"""
    try:
        history = EncryptionHistory.query.order_by(EncryptionHistory.timestamp.desc()).limit(50).all()
        return [item.to_dict() for item in history]
    except Exception as e:
        app.logger.error(f"Error retrieving history: {str(e)}")
        return []

# Routes
@app.route('/')
def index():
    # Get recent history to display on the main page
    history = get_history()
    return render_template('index.html', history=history)

@app.route('/encrypt-text', methods=['POST'])
def encrypt_text():
    data = request.get_json()
    text = data.get('text', '')
    password = data.get('password', '')
    
    if not text or not password:
        return jsonify({'error': 'Text and password are required'}), 400
    
    try:
        crypto = QuantumSafeEncryption(password)
        encrypted = crypto.encrypt(text.encode('utf-8'))
        
        # Log the encryption operation
        log_operation(
            operation_type='encrypt',
            content_type='text',
            original_name=f"Text ({len(text)} chars)",
            result_name=f"Encrypted ({len(encrypted)} chars)"
        )
        
        return jsonify({
            'encrypted': encrypted.decode('utf-8'),
            'history': get_history()
        })
    except Exception as e:
        app.logger.error(f"Error in encrypt_text: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/decrypt-text', methods=['POST'])
def decrypt_text():
    app.logger.info('Decrypt text request received')
    
    try:
        data = request.get_json()
        if not data:
            app.logger.error('No JSON data received')
            return jsonify({'error': 'No data received'}), 400
            
        encrypted_text = data.get('encrypted_text', '')
        password = data.get('password', '')
        
        app.logger.debug(f'Request data - encrypted_text length: {len(encrypted_text)}, password provided: {bool(password)}')
        
        if not encrypted_text or not password:
            app.logger.error('Missing required fields')
            return jsonify({'error': 'Encrypted text and password are required'}), 400
            
        try:
            crypto = QuantumSafeEncryption(password)
            decrypted = crypto.decrypt(encrypted_text.encode('utf-8'))
            decrypted_text = decrypted.decode('utf-8')
            
            # Log the decryption operation
            log_operation(
                operation_type='decrypt',
                content_type='text',
                original_name=f"Encrypted ({len(encrypted_text)} chars)",
                result_name=f"Text ({len(decrypted_text)} chars)"
            )
            
            return jsonify({
                'decrypted': decrypted_text,
                'history': get_history()
            })
            
        except Exception as e:
            error_msg = str(e)
            app.logger.error(f"Error in decrypt_text: {error_msg}")
            
            # Provide more specific error messages based on the exception
            if 'MAC check failed' in error_msg or 'Invalid padding' in error_msg:
                return jsonify({'error': 'Decryption failed. Invalid password or corrupted data.'}), 400
            elif 'Incorrect IV length' in error_msg:
                return jsonify({'error': 'Invalid encrypted data format.'}), 400
            else:
                return jsonify({'error': f'Decryption error: {error_msg}'}), 400
                
    except Exception as e:
        app.logger.error(f"Unexpected error in decrypt_text: {str(e)}")
        return jsonify({'error': 'An unexpected error occurred'}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        app.logger.info("File upload request received")
        
        # Check if the post request has the file part
        if 'file' not in request.files:
            app.logger.error('No file part in the request')
            return jsonify({'error': 'No file part'}), 400
            
        file = request.files['file']
        password = request.form.get('password', '')
        action = request.form.get('action', 'encrypt')
        
        app.logger.debug(f'Request files: {request.files}')
        app.logger.debug(f'File info: filename={file.filename}, content_type={file.content_type}')
        app.logger.debug(f'Action: {action}, Password provided: {bool(password)}')
        
        # Validate input
        if not file or file.filename == '':
            app.logger.error('No file selected')
            return jsonify({'error': 'No file selected'}), 400
            
        if not password:
            app.logger.error('No password provided')
            return jsonify({'error': 'Password is required'}), 400
            
        if action not in ['encrypt', 'decrypt']:
            app.logger.error(f'Invalid action: {action}')
            return jsonify({'error': 'Invalid action'}), 400
            
        # Validate file extension
        if not allowed_file(file.filename):
            app.logger.error(f'File type not allowed: {file.filename}')
            allowed = ', '.join(sorted(app.config['ALLOWED_EXTENSIONS']))
            return jsonify({
                'error': f'File type not allowed. Allowed types: {allowed}'
            }), 400
        
        # Secure the filename and prepare paths
        filename = secure_filename(file.filename)
        if not filename:
            raise ValueError('Invalid filename')
            
        timestamp = int(time.time())
        input_filename = f"{timestamp}_{filename}"
        input_path = os.path.join(app.config['UPLOAD_FOLDER'], input_filename)
        output_filename = f"{timestamp}_{'encrypted' if action == 'encrypt' else 'decrypted'}_{filename}"
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)
        
        # Ensure upload directory exists
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        app.logger.debug(f'Input path: {input_path}, Output path: {output_path}')
        
        # Save the uploaded file
        try:
            file.save(input_path)
            input_size = os.path.getsize(input_path)
            app.logger.info(f'File saved successfully: {input_path} ({input_size} bytes)')
            
            # Process the file
            encryptor = FileEncryptor(password)
            
            if action == 'encrypt':
                app.logger.info(f'Encrypting file: {input_path} -> {output_path}')
                encryptor.encrypt_file(input_path, output_path)
                operation_type = 'encrypt'
                result_desc = f"Encrypted ({os.path.getsize(output_path)} bytes)"
            else:
                app.logger.info(f'Decrypting file: {input_path} -> {output_path}')
                encryptor.decrypt_file(input_path, output_path)
                operation_type = 'decrypt'
                result_desc = f"Decrypted ({os.path.getsize(output_path)} bytes)"
            
            # Verify output file was created
            if not os.path.exists(output_path):
                raise Exception(f'Output file was not created: {output_path}')
                
            output_size = os.path.getsize(output_path)
            app.logger.info(f'File {action}ed successfully. Size: {output_size} bytes')
            
            # Log the file operation
            log_operation(
                operation_type=operation_type,
                content_type='file',
                original_name=f"{filename} ({input_size} bytes)",
                result_name=f"{filename} ({output_size} bytes)"
            )
            
            # Prepare response
            response_data = {
                'status': 'success',
                'message': f'File {action}ed successfully',
                'filename': output_filename,
                'original_size': input_size,
                'processed_size': output_size,
                'action': action,
                'history': get_history()
            }
            
            return jsonify(response_data)
            
        except Exception as e:
            app.logger.error(f"Error processing file: {str(e)}", exc_info=True)
            raise  # Re-raise to be caught by outer try-except
            
        finally:
            # Clean up temporary files
            try:
                if os.path.exists(input_path):
                    os.remove(input_path)
                    app.logger.debug(f'Cleaned up input file: {input_path}')
            except Exception as e:
                app.logger.error(f'Error cleaning up input file: {str(e)}')
                
    except Exception as e:
        app.logger.error(f"Unexpected error in upload_file: {str(e)}", exc_info=True)
        return jsonify({
            'error': f'An error occurred while processing your file: {str(e)}',
            'type': type(e).__name__
        }), 500

@app.route('/download/<filename>')
def download_file(filename):
    try:
        # Security check: Prevent directory traversal
        if '..' in filename or filename.startswith('/'):
            app.logger.warning(f'Potential directory traversal attempt: {filename}')
            return "Invalid filename", 400
            
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        app.logger.info(f'Attempting to download file: {file_path}')
        
        if not os.path.exists(file_path):
            app.logger.error(f'File not found: {file_path}')
            return "File not found", 404
            
        # Determine the original filename for download
        if '_' in filename:
            # Extract the original filename from the encrypted/decrypted filename
            # Format: timestamp_encrypted_original.ext or timestamp_decrypted_original.ext
            parts = filename.split('_')
            if len(parts) >= 3 and parts[1] in ['encrypted', 'decrypted']:
                original_filename = '_'.join(parts[2:])  # Get everything after the second underscore
                download_name = original_filename
            else:
                download_name = filename
        else:
            download_name = filename
            
        app.logger.info(f'Sending file: {file_path} as {download_name}')
        
        # Stream the file for better performance with large files
        response = send_file(
            file_path,
            as_attachment=True,
            download_name=download_name,
            mimetype='application/octet-stream'
        )
        
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        
        return response
        
    except Exception as e:
        app.logger.error(f"Error downloading file {filename}: {str(e)}", exc_info=True)
        return "An error occurred while downloading the file", 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
