import os
import sys
import getpass
import argparse
from pathlib import Path
from crypto_engine import QuantumSafeEncryption, FileEncryptor

def print_banner():
    """Print the application banner."""
    banner = """
    ██████╗ ██████╗ ██╗   ██╗██████╗ ████████╗ ██████╗ 
    ██╔════╝██╔═══██╗╚██╗ ██╔╝██╔══██╗╚══██╔══╝██╔═══██╗
    ██║     ██║   ██║ ╚████╔╝ ██████╔╝   ██║   ██║   ██║
    ██║     ██║   ██║  ╚██╔╝  ██╔═══╝    ██║   ██║   ██║
    ╚██████╗╚██████╔╝   ██║   ██║        ██║   ╚██████╔╝
     ╚═════╝ ╚═════╝    ╚═╝   ╚═╝        ╚═╝    ╚═════╝ 
    Quantum-Safe Encryption Tool v1.0
    """
    print(banner)

def get_password(confirm: bool = False) -> str:
    """Securely get password from user."""
    while True:
        password = getpass.getpass("Enter password: ")
        if not password:
            print("Password cannot be empty!")
            continue
            
        if confirm:
            confirm_pwd = getpass.getpass("Confirm password: ")
            if password != confirm_pwd:
                print("Passwords do not match!")
                continue
        return password

def encrypt_text():
    """Encrypt text input."""
    password = get_password(confirm=True)
    text = input("Enter text to encrypt: ")
    
    crypto = QuantumSafeEncryption(password)
    encrypted = crypto.encrypt(text.encode('utf-8'))
    
    print("\nEncrypted text:")
    print(encrypted.decode('utf-8'))


def decrypt_text():
    """Decrypt text input."""
    password = get_password()
    encrypted_text = input("Enter encrypted text: ")
    
    try:
        crypto = QuantumSafeEncryption(password)
        decrypted = crypto.decrypt(encrypted_text.encode('utf-8'))
        
        print("\nDecrypted text:")
        print(decrypted.decode('utf-8'))
    except Exception as e:
        print(f"Error: {str(e)}")


def encrypt_file():
    """Encrypt a file."""
    password = get_password(confirm=True)
    input_file = input("Enter input file path: ").strip('"')
    
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found!")
        return
        
    output_file = input("Enter output file path (press Enter to use .encrypted extension): ").strip('"')
    if not output_file:
        output_file = f"{input_file}.encrypted"
    
    try:
        encryptor = FileEncryptor(password)
        encryptor.encrypt_file(input_file, output_file)
        print(f"\nFile encrypted successfully: {output_file}")
    except Exception as e:
        print(f"Error: {str(e)}")


def decrypt_file():
    """Decrypt a file."""
    password = get_password()
    input_file = input("Enter input file path: ").strip('"')
    
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found!")
        return
        
    output_file = input("Enter output file path: ").strip('"')
    if not output_file:
        if input_file.endswith('.encrypted'):
            output_file = input_file[:-10]  # Remove .encrypted extension
        else:
            output_file = f"{input_file}.decrypted"
    
    try:
        encryptor = FileEncryptor(password)
        encryptor.decrypt_file(input_file, output_file)
        print(f"\nFile decrypted successfully: {output_file}")
    except Exception as e:
        print(f"Error: {str(e)}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Quantum-Safe Encryption Tool')
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # Text encryption/decryption
    subparsers.add_parser('encrypt-text', help='Encrypt text')
    subparsers.add_parser('decrypt-text', help='Decrypt text')
    
    # File encryption/decryption
    subparsers.add_parser('encrypt-file', help='Encrypt a file')
    subparsers.add_parser('decrypt-file', help='Decrypt a file')
    
    args = parser.parse_args()
    
    print_banner()
    
    if not args.command:
        # Interactive mode
        while True:
            print("\nOptions:")
            print("1. Encrypt text")
            print("2. Decrypt text")
            print("3. Encrypt file")
            print("4. Decrypt file")
            print("5. Exit")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == '1':
                encrypt_text()
            elif choice == '2':
                decrypt_text()
            elif choice == '3':
                encrypt_file()
            elif choice == '4':
                decrypt_file()
            elif choice == '5':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please try again.")
    else:
        # Command-line mode
        if args.command == 'encrypt-text':
            encrypt_text()
        elif args.command == 'decrypt-text':
            decrypt_text()
        elif args.command == 'encrypt-file':
            encrypt_file()
        elif args.command == 'decrypt-file':
            decrypt_file()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
