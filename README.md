# نظام التشفير الآمن

أداة تشفير قوية وسهلة الاستخدام توفر تشفيرًا متعدد الطبقات للنصوص والملفات باستخدام خوارزميات تشفير متقدمة.

## المميزات

- تشفير متعدد الطبقات باستخدام AES-256 و ChaCha20
- حماية كلمات المرور باستخدام خوارزمية PBKDF2 و scrypt
- التحقق من سلامة البيانات باستخدام HMAC-SHA256
- واجهة سهلة الاستخدام
- تشفير وفك تشفير النصوص والملفات
- سريع وآمن

## التثبيت

1. تأكد من تثبيت Python 3.8 أو أحدث
2. قم بتثبيت المتطلبات:

```bash
pip install -r requirements.txt
```

## طريقة الاستخدام

### الوضع التفاعلي

```bash
python crypto_cli.py
```

اتبع التعليمات التي تظهر على الشاشة.

### أوامر سطر الأوامر

```bash
# تشفير النص
python crypto_cli.py encrypt-text

# فك تشفير النص
python crypto_cli.py decrypt-text

# تشفير ملف
python crypto_cli.py encrypt-file

# فك تشفير ملف
python crypto_cli.py decrypt-file
```

## الأمان

- يستخدم النظام تشفيرًا متعدد الطبقات
- كلمات المرور لا يتم تخزينها مطلقًا
- يتم التحقق من سلامة البيانات قبل فك التشفير
- استخدام معلمات آمنة لاشتقاق المفاتيح

---

# Secure Encryption System

A powerful and easy-to-use encryption tool that provides multi-layered encryption for text and files using advanced encryption algorithms.

## Features

- Multi-layered encryption using AES-256 and ChaCha20
- Password protection using PBKDF2 and scrypt
- Data integrity verification using HMAC-SHA256
- User-friendly interface
- Text and file encryption/decryption
- Fast and secure

## Installation

1. Make sure you have Python 3.8 or later installed
2. Install the requirements:

```bash
pip install -r requirements.txt
```

## Usage

### Interactive Mode

```bash
python crypto_cli.py
```

Follow the on-screen instructions.

### Command Line Mode

```bash
# Encrypt text
python crypto_cli.py encrypt-text

# Decrypt text
python crypto_cli.py decrypt-text

# Encrypt file
python crypto_cli.py encrypt-file

# Decrypt file
python crypto_cli.py decrypt-file
```

## Security

- Uses multi-layered encryption
- Passwords are never stored
- Data integrity is verified before decryption
- Secure key derivation parameters
