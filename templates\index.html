{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12 text-center mb-5">
        <h1 class="display-4">نظام التشفير الآمن</h1>
        <p class="lead">أداة تشفير قوية وسهلة الاستخدام لتشفير النصوص والملفات</p>
        <hr class="my-4">
    </div>
</div>

<div class="row mb-5" id="text-encryption">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-file-earmark-lock"></i> تشفير النصوص
                </h4>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="textTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="encrypt-tab" data-bs-toggle="tab" data-bs-target="#encrypt" type="button" role="tab">
                            <i class="bi bi-lock"></i> تشفير
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="decrypt-tab" data-bs-toggle="tab" data-bs-target="#decrypt" type="button" role="tab">
                            <i class="bi bi-unlock"></i> فك التشفير
                        </button>
                    </li>
                </ul>
                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="textTabsContent">
                    <div class="tab-pane fade show active" id="encrypt" role="tabpanel">
                        <form id="encryptForm">
                            <div class="mb-3">
                                <label for="plainText" class="form-label">النص الأصلي</label>
                                <textarea class="form-control" id="plainText" rows="5" placeholder="أدخل النص الذي تريد تشفيره..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="encryptPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="encryptPassword" placeholder="أدخل كلمة مرور قوية">
                                <div class="form-text">لا تشارك كلمة المرور مع أي شخص.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-lock"></i> تشفير
                            </button>
                        </form>
                        <div id="encryptResult" class="mt-3" style="display: none;">
                            <h5>النص المشفر:</h5>
                            <div class="alert alert-secondary">
                                <pre id="encryptedText" class="mb-0"></pre>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('encryptedText')">
                                <i class="bi bi-clipboard"></i> نسخ
                            </button>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="decrypt" role="tabpanel">
                        <form id="decryptForm">
                            <div class="mb-3">
                                <label for="encryptedTextInput" class="form-label">النص المشفر</label>
                                <div class="input-group">
                                    <textarea class="form-control" id="encryptedTextInput" rows="5" placeholder="الصق النص المشفر هنا" dir="auto"></textarea>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-target="encryptedTextInput" title="نسخ النص">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary clear-btn" type="button" data-target="encryptedTextInput" title="مسح النص">
                                        <i class="bi bi-x-lg"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="decryptPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="decryptPassword" placeholder="أدخل كلمة المرور المستخدمة في التشفير">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-unlock"></i> فك التشفير
                            </button>
                        </form>
                        <div id="decryptResult" class="mt-3" style="display: none;">
                            <h5>النص الأصلي:</h5>
                            <div class="alert alert-success">
                                <pre id="decryptedText" class="mb-0"></pre>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('decryptedText')">
                                <i class="bi bi-clipboard"></i> نسخ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5" id="file-encryption">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="bi bi-file-earmark-lock2"></i> تشفير الملفات
                </h4>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="fileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="encrypt-file-tab" data-bs-toggle="tab" data-bs-target="#encrypt-file" type="button" role="tab">
                            <i class="bi bi-file-earmark-lock"></i> تشفير ملف
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="decrypt-file-tab" data-bs-toggle="tab" data-bs-target="#decrypt-file" type="button" role="tab">
                            <i class="bi bi-file-earmark-unlock"></i> فك تشفير ملف
                        </button>
                    </li>
                </ul>
                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="fileTabsContent">
                    <div class="tab-pane fade show active" id="encrypt-file" role="tabpanel">
                        <form id="encryptFileForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="fileToEncrypt" class="form-label">اختر ملفًا للتشفير</label>
                                <input class="form-control" type="file" id="fileToEncrypt" name="file">
                                <div class="form-text">الحد الأقصى لحجم الملف: 16 ميجابايت</div>
                            </div>
                            <div class="mb-3">
                                <label for="fileEncryptPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="fileEncryptPassword" name="password" placeholder="أدخل كلمة مرور قوية">
                                <div class="form-text">ستحتاج إلى هذه الكلمة لاحقًا لفك تشفير الملف.</div>
                            </div>
                            <input type="hidden" name="action" value="encrypt">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-file-earmark-lock"></i> تشفير الملف
                            </button>
                        </form>
                        <div id="encryptFileResult" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <p class="mb-1">تم تشفير الملف بنجاح!</p>
                                <p class="mb-1">اسم الملف: <span id="encryptedFileName"></span></p>
                                <a href="#" id="downloadEncryptedFile" class="btn btn-sm btn-outline-success mt-2">
                                    <i class="bi bi-download"></i> تحميل الملف المشفر
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="decrypt-file" role="tabpanel">
                        <form id="decryptFileForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="fileToDecrypt" class="form-label">اختر ملفًا لفك تشفيره</label>
                                <input class="form-control" type="file" id="fileToDecrypt" name="file">
                                <div class="form-text">يجب أن يكون الملف مشفرًا مسبقًا باستخدام هذه الأداة.</div>
                            </div>
                            <div class="mb-3">
                                <label for="fileDecryptPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="fileDecryptPassword" name="password" placeholder="أدخل كلمة المرور المستخدمة في التشفير">
                                <div class="form-text">يجب أن تكون كلمة المرور صحيحة لاستعادة الملف الأصلي.</div>
                            </div>
                            <input type="hidden" name="action" value="decrypt">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-file-earmark-unlock"></i> فك تشفير الملف
                            </button>
                        </form>
                        <div id="decryptFileResult" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <p class="mb-1">تم فك تشفير الملف بنجاح!</p>
                                <p class="mb-1">اسم الملف: <span id="decryptedFileName"></span></p>
                                <a href="#" id="downloadDecryptedFile" class="btn btn-sm btn-outline-success mt-2">
                                    <i class="bi bi-download"></i> تحميل الملف الأصلي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-clock-history"></i> سجل العمليات
                </h4>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="historyTable">
                        <thead class="table-light">
                            <tr>
                                <th>الوقت</th>
                                <th>العملية</th>
                                <th>النوع</th>
                                <th>الملف/النص</th>
                                <th>النتيجة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if history %}
                                {% for item in history %}
                                <tr>
                                    <td>{{ item.timestamp }}</td>
                                    <td>
                                        {% if item.operation_type == 'encrypt' %}
                                            <span class="badge bg-success">تشفير</span>
                                        {% else %}
                                            <span class="badge bg-info text-dark">فك التشفير</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.content_type == 'file' %}
                                            <i class="bi bi-file-earmark"></i> ملف
                                        {% else %}
                                            <i class="bi bi-text-paragraph"></i> نص
                                        {% endif %}
                                    </td>
                                    <td>{{ item.original_name }}</td>
                                    <td>{{ item.result_name }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <i class="bi bi-info-circle"></i> لا توجد عمليات سابقة
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="bi bi-shield-lock"></i> حول نظام التشفير
                </h4>
            </div>
            <div class="card-body">
                <h5>ميزات النظام:</h5>
                <ul>
                    <li>تشفير قوي باستخدام خوارزميتي AES-256 و ChaCha20</li>
                    <li>حماية كلمات المرور باستخدام خوارزمية PBKDF2 و scrypt</li>
                    <li>التحقق من سلامة البيانات باستخدام HMAC-SHA256</li>
                    <li>واجهة سهلة الاستخدام باللغة العربية</li>
                    <li>يدعم تشفير النصوص والملفات</li>
                    <li>سجل كامل لعمليات التشفير وفك التشفير</li>
                </ul>
                <div class="alert alert-warning">
                    <strong>هام:</strong> لا تقم بتشفير الملفات الحساسة باستخدام كلمات مرور ضعيفة.
                    لا يمكن استعادة الملفات المشفرة في حالة فقدان كلمة المرور.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
