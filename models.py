from datetime import datetime
from app import db

class EncryptionHistory(db.Model):
    """Model to store history of encryption/decryption operations"""
    id = db.Column(db.Integer, primary_key=True)
    operation_type = db.Column(db.String(10), nullable=False)  # 'encrypt' or 'decrypt'
    content_type = db.Column(db.String(10), nullable=False)    # 'text' or 'file'
    original_name = db.Column(db.String(255))                  # Original filename or 'text'
    result_name = db.Column(db.String(255))                    # Resulting filename or encrypted text preview
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert the history item to a dictionary for JSON serialization"""
        return {
            'id': self.id,
            'operation_type': self.operation_type,
            'content_type': self.content_type,
            'original_name': self.original_name,
            'result_name': self.result_name,
            'timestamp': self.timestamp.strftime('%Y-%m-%d %H:%M:%S')
        }

class SavedText(db.Model):
    """Model to store saved encrypted/decrypted texts"""
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    is_encrypted = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert the saved text to a dictionary for JSON serialization"""
        return {
            'id': self.id,
            'content_preview': self.content[:100] + ('...' if len(self.content) > 100 else ''),
            'is_encrypted': self.is_encrypted,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
