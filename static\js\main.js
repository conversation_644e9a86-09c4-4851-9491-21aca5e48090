// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Language switch functionality
document.addEventListener('DOMContentLoaded', function() {
    const languageSwitch = document.getElementById('languageSwitch');
    
    // Check saved language preference
    const savedLang = localStorage.getItem('language') || 'ar';
    if (savedLang === 'en') {
        document.documentElement.dir = 'ltr';
        document.documentElement.lang = 'en';
        if (languageSwitch) languageSwitch.checked = true;
    }
    
    // Toggle language
    if (languageSwitch) {
        languageSwitch.addEventListener('change', function() {
            if (this.checked) {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
                localStorage.setItem('language', 'en');
            } else {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
                localStorage.setItem('language', 'ar');
            }
            // Reload to apply language changes
            location.reload();
        });
    }
});

// Text Encryption Form
const encryptForm = document.getElementById('encryptForm');
if (encryptForm) {
    encryptForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const plainText = document.getElementById('plainText').value;
        const password = document.getElementById('encryptPassword').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        if (!plainText || !password) {
            showAlert('الرجاء إدخال النص وكلمة المرور', 'danger');
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التشفير...';
        
        try {
            // Get CSRF token from meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const response = await fetch('/encrypt-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    text: plainText,
                    password: password
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                document.getElementById('encryptedText').textContent = data.encrypted;
                document.getElementById('encryptResult').style.display = 'block';
                // Update history if available
                if (data.history) {
                    updateHistoryTable(data.history);
                }
                // Scroll to result
                document.getElementById('encryptResult').scrollIntoView({ behavior: 'smooth' });
            } else {
                throw new Error(data.error || 'فشل في التشفير');
            }
        } catch (error) {
            showAlert(error.message, 'danger');
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }
    });
}

// Text Decryption Form
const decryptForm = document.getElementById('decryptForm');
if (decryptForm) {
    decryptForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const encryptedText = document.getElementById('encryptedTextInput').value;
        const password = document.getElementById('decryptPassword').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        if (!encryptedText || !password) {
            showAlert('الرجاء إدخال النص المشفر وكلمة المرور', 'danger');
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري فك التشفير...';
        
        try {
            // Get CSRF token from meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const response = await fetch('/decrypt-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    encrypted_text: encryptedText,
                    password: password
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                document.getElementById('decryptedText').textContent = data.decrypted;
                document.getElementById('decryptResult').style.display = 'block';
                // Update history if available
                if (data.history) {
                    updateHistoryTable(data.history);
                }
                // Scroll to result
                document.getElementById('decryptResult').scrollIntoView({ behavior: 'smooth' });
            } else {
                throw new Error(data.error || 'فشل في فك التشفير');
            }
        } catch (error) {
            showAlert('فشل في فك التشفير. تأكد من صحة النص المشفر وكلمة المرور.', 'danger');
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }
    });
}

// File Encryption Form
const encryptFileForm = document.getElementById('encryptFileForm');
if (encryptFileForm) {
    encryptFileForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('fileToEncrypt');
        const password = document.getElementById('fileEncryptPassword').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        if (!fileInput.files.length) {
            showAlert('الرجاء اختيار ملف', 'danger');
            return;
        }
        
        if (!password) {
            showAlert('الرجاء إدخال كلمة المرور', 'danger');
            return;
        }
        
        const formData = new FormData(this);
        formData.append('action', 'encrypt');  // Add action parameter
        
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التشفير...';
        
        try {
            const response = await fetch('/upload', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken  // Add CSRF token to headers
                },
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok) {
                // Auto-download the encrypted/decrypted file
                const downloadUrl = `/download/${data.filename}`;
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = data.filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                // Update UI with success message
                document.getElementById('encryptedFileName').textContent = data.filename;
                document.getElementById('encryptFileResult').style.display = 'block';
                
                // Update history if available
                if (data.history) {
                    updateHistoryTable(data.history);
                }
                
                // Scroll to result
                document.getElementById('encryptFileResult').scrollIntoView({ behavior: 'smooth' });
                
                // Reset form
                this.reset();
            } else {
                throw new Error(data.error || 'فشل في تشفير الملف');
            }
        } catch (error) {
            showAlert(error.message, 'danger');
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }
    });
}

// File Decryption Form
const decryptFileForm = document.getElementById('decryptFileForm');
if (decryptFileForm) {
    decryptFileForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('fileToDecrypt');
        const password = document.getElementById('fileDecryptPassword').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        if (!fileInput.files.length) {
            showAlert('الرجاء اختيار ملف مشفر', 'danger');
            return;
        }
        
        if (!password) {
            showAlert('الرجاء إدخال كلمة المرور', 'danger');
            return;
        }
        
        const formData = new FormData(this);
        formData.append('action', 'decrypt');  // Add action parameter
        
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري فك التشفير...';
        
        try {
            const response = await fetch('/upload', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken  // Add CSRF token to headers
                },
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok) {
                // Auto-download the decrypted file
                const downloadUrl = `/download/${data.filename}`;
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = data.filename.replace('decrypted_', ''); // Remove the 'decrypted_' prefix
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                // Update UI with success message
                document.getElementById('decryptedFileName').textContent = data.filename.replace('decrypted_', '');
                document.getElementById('decryptFileResult').style.display = 'block';
                
                // Update history if available
                if (data.history) {
                    updateHistoryTable(data.history);
                }
                
                // Scroll to result
                document.getElementById('decryptFileResult').scrollIntoView({ behavior: 'smooth' });
                
                // Reset form
                this.reset();
            } else {
                throw new Error(data.error || 'فشل في فك تشفير الملف');
            }
        } catch (error) {
            showAlert('فشل في فك تشفير الملف. تأكد من صحة الملف وكلمة المرور.', 'danger');
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }
    });
}

// Helper function to copy text to clipboard
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        showAlert('تم نسخ النص بنجاح', 'success');
    }).catch(function() {
        showAlert('فشل في نسخ النص', 'danger');
    });
}

// Helper function to update history table
function updateHistoryTable(historyData) {
    const tbody = document.querySelector('#historyTable tbody');
    if (!tbody) return;
    
    // Clear existing rows
    tbody.innerHTML = '';
    
    if (!historyData || historyData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-4">
                    <i class="bi bi-info-circle"></i> لا توجد عمليات سابقة
                </td>
            </tr>`;
        return;
    }
    
    // Add new rows
    historyData.forEach(item => {
        const row = document.createElement('tr');
        const opType = item.operation_type === 'encrypt' ? 
            '<span class="badge bg-success">تشفير</span>' : 
            '<span class="badge bg-info text-dark">فك التشفير</span>';
            
        const contentType = item.content_type === 'file' ? 
            '<i class="bi bi-file-earmark"></i> ملف' : 
            '<i class="bi bi-text-paragraph"></i> نص';
            
        row.innerHTML = `
            <td>${new Date(item.timestamp).toLocaleString()}</td>
            <td>${opType}</td>
            <td>${contentType}</td>
            <td>${item.original_name}</td>
            <td>${item.result_name}</td>
        `;
        tbody.appendChild(row);
    });
}

// Helper function to show alerts
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add the alert to the alerts container
    const alertsContainer = document.getElementById('alertsContainer');
    if (alertsContainer) {
        alertsContainer.prepend(alertDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 5000);
    }
}

// Initialize tooltips on tab show to handle dynamically loaded content
document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
    tab.addEventListener('shown.bs.tab', function (e) {
        // Reinitialize tooltips for the active tab
        var tooltipTriggerList = [].slice.call(e.target.closest('.tab-pane').querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
});
